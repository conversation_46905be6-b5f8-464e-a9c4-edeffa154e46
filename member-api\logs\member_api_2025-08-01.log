2025-08-01 14:19:33 - main - INFO - lifespan:23 - 应用启动中...
2025-08-01 14:19:33 - core.database - INFO - connect:26 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-01 14:19:33 - core.database - INFO - connect:40 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-01 14:19:33 - core.database - INFO - connect:43 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-01 14:19:33 - core.database - INFO - connect:57 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-01 14:19:33 - core.database - INFO - connect:60 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-01 14:19:34 - core.database - INFO - connect:74 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-01 14:19:34 - core.database - INFO - connect:77 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-01 14:19:34 - core.database - INFO - connect:88 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-01 14:19:34 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:19:34 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:34 - core.database - INFO - _test_database_connections:105 - dwoutput数据库连接测试成功: (1, 339431313, 'dwoutput')
2025-08-01 14:19:34 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:19:34 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:34 - core.database - INFO - _test_database_connections:112 - wedatas数据库连接测试成功: (1, 607869021, 'wedatas')
2025-08-01 14:19:34 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-01 14:19:34 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:34 - core.database - INFO - _test_database_connections:121 - welife_hydb数据库连接测试成功: (1,)
2025-08-01 14:19:34 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-01 14:19:34 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:34 - core.database - INFO - _test_database_connections:127 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-01 14:19:34 - core.database - INFO - _test_database_connections:138 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-01 14:19:34 - main - INFO - lifespan:27 - 数据库连接已建立
2025-08-01 14:19:53 - request - INFO - log_requests:70 - 收到请求: POST http://127.0.0.1:8000/api/query/data/member-base
2025-08-01 14:19:53 - request - INFO - log_requests:71 - 请求头: {'host': '127.0.0.1:8000', 'connection': 'close', 'content-length': '182', 'x-request-id': 'mdsfoyz4yofd1yais9', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.q1NGewDxxUAYck01iXXFBZls-pt-zBfmFaFSNwYxSq4', 'sec-ch-ua-platform': '"Windows"', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/json', 'origin': 'http://localhost:5173', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:5173/query', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': '_ga=GA1.1.833588396.1753086687; _ga_R1FN4KJKJH=GS2.1.s1753233947$o2$g0$t1753233947$j60$l0$h0'}
2025-08-01 14:19:53 - request - INFO - log_requests:70 - 收到请求: POST http://127.0.0.1:8000/api/query/data/member-charge
2025-08-01 14:19:53 - request - INFO - log_requests:71 - 请求头: {'host': '127.0.0.1:8000', 'connection': 'close', 'content-length': '182', 'x-request-id': 'mdsfoyz48cyfz0enwvm', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.q1NGewDxxUAYck01iXXFBZls-pt-zBfmFaFSNwYxSq4', 'sec-ch-ua-platform': '"Windows"', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/json', 'origin': 'http://localhost:5173', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:5173/query', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': '_ga=GA1.1.833588396.1753086687; _ga_R1FN4KJKJH=GS2.1.s1753233947$o2$g0$t1753233947$j60$l0$h0'}
2025-08-01 14:19:53 - request - INFO - log_requests:70 - 收到请求: POST http://127.0.0.1:8000/api/query/data/member-consume
2025-08-01 14:19:53 - request - INFO - log_requests:71 - 请求头: {'host': '127.0.0.1:8000', 'connection': 'close', 'content-length': '182', 'x-request-id': 'mdsfoyz4jjax3wdnxq', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.q1NGewDxxUAYck01iXXFBZls-pt-zBfmFaFSNwYxSq4', 'sec-ch-ua-platform': '"Windows"', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/json', 'origin': 'http://localhost:5173', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:5173/query', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': '_ga=GA1.1.833588396.1753086687; _ga_R1FN4KJKJH=GS2.1.s1753233947$o2$g0$t1753233947$j60$l0$h0'}
2025-08-01 14:19:53 - request - INFO - log_requests:70 - 收到请求: POST http://127.0.0.1:8000/api/query/data/coupon-trade
2025-08-01 14:19:53 - request - INFO - log_requests:71 - 请求头: {'host': '127.0.0.1:8000', 'connection': 'close', 'content-length': '182', 'x-request-id': 'mdsfoyz4l7s89x9hj59', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.q1NGewDxxUAYck01iXXFBZls-pt-zBfmFaFSNwYxSq4', 'sec-ch-ua-platform': '"Windows"', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/json', 'origin': 'http://localhost:5173', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:5173/query', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': '_ga=GA1.1.833588396.1753086687; _ga_R1FN4KJKJH=GS2.1.s1753233947$o2$g0$t1753233947$j60$l0$h0'}
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - get_member_base_data:402 - 收到会员基础数据查询请求 - bid: 3064710828, sid: None, 日期: 2025-07-01~2025-07-07
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_member_base_data:290 - 开始获取会员基础数据 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_member_base_data:290 - 开始获取会员基础数据 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_member_base_data:290 - 开始获取会员基础数据 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:80 - 开始查询wedatas数据库 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:19:53 - api.query.MemberBaseTab - WARNING - _fetch_wedatas_data:81 - 注意：查询日期为 20250701-20250707，请确认这是正确的日期范围！
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:153 - 开始查询dwoutput数据库 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:161 - 累计消费会员查询SQL: SELECT 
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_consomer
        
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:80 - 开始查询wedatas数据库 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:19:53 - api.query.MemberBaseTab - WARNING - _fetch_wedatas_data:81 - 注意：查询日期为 20250624-20250630，请确认这是正确的日期范围！
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:153 - 开始查询dwoutput数据库 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:161 - 累计消费会员查询SQL: SELECT 
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_consomer
        
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:80 - 开始查询wedatas数据库 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:19:53 - api.query.MemberBaseTab - WARNING - _fetch_wedatas_data:81 - 注意：查询日期为 20240701-20240707，请确认这是正确的日期范围！
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:153 - 开始查询dwoutput数据库 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:161 - 累计消费会员查询SQL: SELECT 
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_consomer
        
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - get_member_charge_data:248 - 收到会员充值数据查询请求 - bid: 3064710828, sid: None, 日期: 2025-07-01~2025-07-07
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - get_member_consume_data:319 - 收到会员消费数据查询请求 - bid: 3064710828, sid: None, 日期: 2025-07-01~2025-07-07
2025-08-01 14:19:53 - api.query.CouponTradeTab - INFO - get_coupon_trade_data:21 - 收到券交易数据查询请求 - bid: 3064710828, sid: None, 查询类型: week
2025-08-01 14:19:53 - api.query.CouponTradeTab - INFO - get_coupon_trade_data:25 - 原始传入日期: 2025-07-01 ~ 2025-07-07
2025-08-01 14:19:53 - api.query.CouponTradeTab - INFO - get_coupon_trade_data:26 - 实际查询日期: 2025-07-01 ~ 2025-07-07
2025-08-01 14:19:53 - api.query.CouponTradeTab - INFO - get_coupon_trade_data:29 - 开始查询券交易数据（仅当前期间）
2025-08-01 14:19:53 - api.query.CouponTradeTab - INFO - _get_coupon_data:58 - 开始查询wedatas券交易数据 - bid: 3064710828, sid: None, 时间范围: 2025-07-01-2025-07-07
2025-08-01 14:19:53 - api.query.CouponTradeTab - INFO - _get_coupon_data:112 - 执行SQL: 
            SELECT
                couponname,
                couponid,
                
                -- 券发放量（发放 - 取消）
                SUM(coupon_send) - SUM(cancel_coupon_send) AS coupon_sent,

                -- 券使用量（使用 - 取消）
                SUM(coupon_used) - SUM(cancel_coupon_used) AS coupon_used,

                -- 使用率 = 使用量 / 发放量（防止除以0）
                ROUND(
                    (SUM(coupon_used) - SUM(cancel_coupon_used)) / 
                    NULLIF(SUM(coupon_send) - SUM(cancel_coupon_send), 0),
                    4
                ) AS coupon_use_rate,

                -- 券抵扣金额 = 使用量 × 单张金额 camount
                SUM((coupon_used - cancel_coupon_used) * camount) AS coupon_discount_amount,

                -- 带动储值消费（储值 - 取消）
                SUM(trade_prepay) - SUM(cancel_trade_prepay) AS trade_prepay_amount,

                -- 带动现金消费（现金 - 取消）
                SUM(trade_cash) - SUM(cancel_trade_cash) AS trade_cash_amount,

                -- 总交易金额（总额 - 取消）
                SUM(trade_amount) - SUM(cancel_trade_amount) AS trade_total_amount

            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN %s AND %s
                AND bid = %s
            
            GROUP BY couponname, couponid
            ORDER BY (SUM(trade_cash) - SUM(cancel_trade_cash)) DESC
            
2025-08-01 14:19:53 - api.query.CouponTradeTab - INFO - _get_coupon_data:113 - 参数: ['20250701', '20250707', '3064710828']
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_member_charge_data:154 - 开始获取会员充值数据 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_member_charge_data:154 - 开始获取会员充值数据 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_member_charge_data:154 - 开始获取会员充值数据 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_member_consume_data:202 - 开始获取会员消费数据 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_member_consume_data:202 - 开始获取会员消费数据 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_member_consume_data:202 - 开始获取会员消费数据 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:62 - 开始查询dwoutput充值基础数据 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:132 - 开始查询dwoutput储值消耗详情 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:138 - 储值消耗详情查询SQL: 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250701 AND 20250707 AND bid = 3064710828 )
        ) AS total_consume_prepay_used
        
            
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:62 - 开始查询dwoutput充值基础数据 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:132 - 开始查询dwoutput储值消耗详情 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:138 - 储值消耗详情查询SQL: 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250623 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250624 AND 20250630 AND bid = 3064710828 )
        ) AS total_consume_prepay_used
        
            
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:62 - 开始查询dwoutput充值基础数据 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:132 - 开始查询dwoutput储值消耗详情 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:138 - 储值消耗详情查询SQL: 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240701 AND 20240707 AND bid = 3064710828 )
        ) AS total_consume_prepay_used
        
            
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:63 - 开始查询dwoutput消费基础数据 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_amount_virtual查询SQL: SELECT 
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:136 - 开始查询dwoutput储值使用详情 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:149 - 储值使用详情查询SQL: 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250701 AND 20250707 AND bid = 3064710828 )
        ) AS total_prepay_used_real
        
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:169 - 开始查询wedatas券交易数据 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:182 - 券交易查询SQL: 
            SELECT 
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:63 - 开始查询dwoutput消费基础数据 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_amount_virtual查询SQL: SELECT 
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:136 - 开始查询dwoutput储值使用详情 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:149 - 储值使用详情查询SQL: 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250623 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250624 AND 20250630 AND bid = 3064710828 )
        ) AS total_prepay_used_real
        
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:169 - 开始查询wedatas券交易数据 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:182 - 券交易查询SQL: 
            SELECT 
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:63 - 开始查询dwoutput消费基础数据 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_amount_virtual查询SQL: SELECT 
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:136 - 开始查询dwoutput储值使用详情 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:149 - 储值使用详情查询SQL: 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240701 AND 20240707 AND bid = 3064710828 )
        ) AS total_prepay_used_real
        
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:169 - 开始查询wedatas券交易数据 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:182 - 券交易查询SQL: 
            SELECT 
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.020秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT
                couponname,
                couponid,
                
                -- 券发放量（发放 - 取消）
                SUM(coupon_send) - SUM(cancel_coupon_send) AS coupon_sent,

                -- 券使用量（使用 - 取消）
                SUM(coupon_used) - SUM(cancel_coupon_used) AS coupon_used,

                -- 使用率 = 使用量 / 发放量（防止除以0）
                ROUND(
                    (SUM(coupon_used) - SUM(cancel_coupon_used)) / 
                    NULLIF(SUM(coupon_send) - SUM(cancel_coupon_send), 0),
                    4
                ) AS coupon_use_rate,

                -- 券抵扣金额 = 使用量 × 单张金额 camount
                SUM((coupon_used - cancel_coupon_used) * camount) AS coupon_discount_amount,

                -- 带动储值消费（储值 - 取消）
                SUM(trade_prepay) - SUM(cancel_trade_prepay) AS trade_prepay_amount,

                -- 带动现金消费（现金 - 取消）
                SUM(trade_cash) - SUM(cancel_trade_cash) AS trade_cash_amount,

                -- 总交易金额（总额 - 取消）
                SUM(trade_amount) - SUM(cancel_trade_amount) AS trade_total_amount

            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN '20250701' AND '20250707'
                AND bid = '3064710828'
            
            GROUP BY couponname, couponid
            ORDER BY (SUM(trade_cash) - SUM(cancel_trade_cash)) DESC
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - ['20250701', '20250707', '3064710828']
2025-08-01 14:19:53 - api.query.CouponTradeTab - INFO - _get_coupon_data:121 - wedatas券交易数据查询完成，耗时: 0.028秒，返回8条记录
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250701 AND 20250707
              AND bid = 3064710828
              
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.026秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250701 AND 20250707 AND bid = 3064710828 )
        ) AS total_consume_prepay_used
        
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.038秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:186 - wedatas券交易数据查询完成，耗时: 0.046秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:187 - 券交易数据结果: {'total_coupon_trade_amount': Decimal('349210')}
2025-08-01 14:19:53 - api.query.CouponTradeTab - INFO - get_coupon_trade_data:34 - 券交易数据查询完成，总耗时: 0.054秒
2025-08-01 14:19:53 - api.query.CouponTradeTab - INFO - get_coupon_trade_data:40 - 券交易数据处理成功 - bid: 3064710828, sid: None
2025-08-01 14:19:53 - request - INFO - log_requests:78 - 请求处理完成: POST http://127.0.0.1:8000/api/query/data/coupon-trade - 状态码: 200 - 耗时: 0.068秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250624 AND 20250630
              AND bid = 3064710828
              
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.072秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250623 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250624 AND 20250630 AND bid = 3064710828 )
        ) AS total_consume_prepay_used
        
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.080秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20240701 AND 20240707
              AND bid = 3064710828
              
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.087秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.089秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:186 - wedatas券交易数据查询完成，耗时: 0.089秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:187 - 券交易数据结果: {'total_coupon_trade_amount': Decimal('238120')}
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.088秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:186 - wedatas券交易数据查询完成，耗时: 0.088秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:187 - 券交易数据结果: {'total_coupon_trade_amount': Decimal('616510')}
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240701 AND 20240707 AND bid = 3064710828 )
        ) AS total_consume_prepay_used
        
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.100秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user + cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.128秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250701 AND 20250707 AND bid = 3064710828 )
        ) AS total_prepay_used_real
        
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.123秒
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:142 - dwoutput储值消耗详情查询完成，耗时: 0.127秒
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:143 - 储值消耗详情结果: {'total_consume_prepay_used': Decimal('1329257')}
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:142 - dwoutput储值消耗详情查询完成，耗时: 0.127秒
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:143 - 储值消耗详情结果: {'total_consume_prepay_used': Decimal('1282297')}
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:142 - dwoutput储值消耗详情查询完成，耗时: 0.127秒
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:143 - 储值消耗详情结果: {'total_consume_prepay_used': Decimal('1536290')}
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:153 - dwoutput储值使用详情查询完成，耗时: 0.127秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:154 - 储值使用详情结果: {'total_prepay_used_real': Decimal('1329257')}
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250623 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250624 AND 20250630 AND bid = 3064710828 )
        ) AS total_prepay_used_real
        
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.149秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user + cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.164秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_consomer
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.162秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user + cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.166秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_consomer
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.167秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_consomer
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.176秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS net_all_user
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.047秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240701 AND 20240707 AND bid = 3064710828 )
        ) AS total_prepay_used_real
        
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.166秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:153 - dwoutput储值使用详情查询完成，耗时: 0.178秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:154 - 储值使用详情结果: {'total_prepay_used_real': Decimal('1282297')}
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:153 - dwoutput储值使用详情查询完成，耗时: 0.176秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:154 - 储值使用详情结果: {'total_prepay_used_real': Decimal('1536290')}
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20240701 AND 20240707
              AND bid = 3064710828
              
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.067秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250701 AND 20250707
              AND bid = 3064710828
              
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.070秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250624 AND 20250630
              AND bid = 3064710828
              
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.072秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20240701 AND 20240707
              AND bid = 3064710828
              
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.012秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.209秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.208秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_pv查询SQL: SELECT 
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.212秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_pv查询SQL: SELECT 
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_pv查询SQL: SELECT 
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250701 AND 20250707
              AND bid = 3064710828
              
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_cancel_user
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.048秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250624 AND 20250630
              AND bid = 3064710828
              
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.018秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:104 - 新增会员查询SQL: SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20240701 AND 20240707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_charger
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.038秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20240701 AND 20240707
              AND bid = 3064710828
              
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.013秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS net_all_user
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.067秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS net_all_user
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.067秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_charger
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.043秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_charger
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.044秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.014秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_real查询SQL: SELECT 
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_real查询SQL: SELECT 
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20240707
         AND bid = 3064710828
         ) AS total_charge_amount_unused
        
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.013秒
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:120 - dwoutput充值基础数据查询完成，耗时: 0.235秒
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:121 - 充值基础数据结果: {'total_charge_pv': Decimal('134'), 'total_charge_cash': Decimal('1770000'), 'total_charge_amount': Decimal('2170000'), 'total_charge_present': Decimal('400000'), 'total_charge_amount_unused': Decimal('7715040')}
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250624 AND 20250630
              AND bid = 3064710828
              
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.018秒
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_member_charge_data:178 - 会员充值数据获取完成，总耗时: 0.239秒，数据字段数: 7
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250701 AND 20250707
              AND bid = 3064710828
              
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.023秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN 20250624 AND 20250630
           AND bid = 3064710828
           ) AS new_user_consomer_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.014秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_uv查询SQL: SELECT 
        SUM(consume_cash_uv) AS total_consume_cash_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_uv查询SQL: SELECT 
        SUM(consume_cash_uv) AS total_consume_cash_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20250630
         AND bid = 3064710828
         ) AS total_charge_amount_unused
        
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.013秒
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:120 - dwoutput充值基础数据查询完成，耗时: 0.250秒
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:121 - 充值基础数据结果: {'total_charge_pv': Decimal('94'), 'total_charge_cash': Decimal('1520000'), 'total_charge_amount': Decimal('1854000'), 'total_charge_present': Decimal('334000'), 'total_charge_amount_unused': Decimal('17739076')}
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_member_charge_data:178 - 会员充值数据获取完成，总耗时: 0.254秒，数据字段数: 7
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.037秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_real查询SQL: SELECT 
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN 20240701 AND 20240707
           AND bid = 3064710828
           ) AS new_user_consomer_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.027秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20250707
         AND bid = 3064710828
         ) AS total_charge_amount_unused
        
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:120 - dwoutput充值基础数据查询完成，耗时: 0.258秒
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:121 - 充值基础数据结果: {'total_charge_pv': Decimal('104'), 'total_charge_cash': Decimal('1770000'), 'total_charge_amount': Decimal('2172000'), 'total_charge_present': Decimal('402000'), 'total_charge_amount_unused': Decimal('18292906')}
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - _fetch_member_charge_data:178 - 会员充值数据获取完成，总耗时: 0.260秒，数据字段数: 7
2025-08-01 14:19:53 - api.query.MemberChargeTab - INFO - get_member_charge_data:279 - 会员充值数据查询成功完成 - bid: 3064710828, sid: None
2025-08-01 14:19:53 - api.query.MemberDataQuery - INFO - get_member_charge_data:161 - 会员充值数据原始对象: charge_count=FieldDataModel(value=104, unit='笔', chain_comparison=[94], chain_change_rate=['+10.64%'], chain_labels=['上期'], year_over_year=134, year_over_year_rate='-22.39%') charge_amount=FieldDataModel(value=17700.0, unit='元', chain_comparison=[15200.0], chain_change_rate=['+16.45%'], chain_labels=['上期'], year_over_year=17700.0, year_over_year_rate='+0.00%') period_charge_amount=FieldDataModel(value=21720.0, unit='元', chain_comparison=[18540.0], chain_change_rate=['+17.15%'], chain_labels=['上期'], year_over_year=21700.0, year_over_year_rate='+0.09%') period_charge_present=FieldDataModel(value=4020.0, unit='元', chain_comparison=[3340.0], chain_change_rate=['+20.36%'], chain_labels=['上期'], year_over_year=4000.0, year_over_year_rate='+0.50%') period_charge_amount_unused=FieldDataModel(value=182929.06, unit='元', chain_comparison=[177390.76], chain_change_rate=['+3.12%'], chain_labels=['上期'], year_over_year=77150.4, year_over_year_rate='+137.11%') consume_prepay_amount=FieldDataModel(value=13292.57, unit='元', chain_comparison=[12822.97], chain_change_rate=['+3.66%'], chain_labels=['上期'], year_over_year=15362.9, year_over_year_rate='-13.48%') retention_rate=FieldDataModel(value=24.9, unit='%', chain_comparison=[15.64], chain_change_rate=['+59.21%'], chain_labels=['上期'], year_over_year=13.2, year_over_year_rate='+88.64%')
2025-08-01 14:19:53 - api.query.MemberDataQuery - INFO - get_member_charge_data:165 - 会员充值数据转换后: {'chargeCount': {'value': 104, 'unit': '笔', 'chainComparison': [94], 'chainChangeRate': ['+10.64%'], 'chainLabels': ['上期'], 'yearOverYear': 134, 'yearOverYearRate': '-22.39%'}, 'chargeAmount': {'value': 17700.0, 'unit': '元', 'chainComparison': [15200.0], 'chainChangeRate': ['+16.45%'], 'chainLabels': ['上期'], 'yearOverYear': 17700.0, 'yearOverYearRate': '+0.00%'}, 'periodChargeAmount': {'value': 21720.0, 'unit': '元', 'chainComparison': [18540.0], 'chainChangeRate': ['+17.15%'], 'chainLabels': ['上期'], 'yearOverYear': 21700.0, 'yearOverYearRate': '+0.09%'}, 'periodChargePresent': {'value': 4020.0, 'unit': '元', 'chainComparison': [3340.0], 'chainChangeRate': ['+20.36%'], 'chainLabels': ['上期'], 'yearOverYear': 4000.0, 'yearOverYearRate': '+0.50%'}, 'periodChargeAmountUnused': {'value': 182929.06, 'unit': '元', 'chainComparison': [177390.76], 'chainChangeRate': ['+3.12%'], 'chainLabels': ['上期'], 'yearOverYear': 77150.4, 'yearOverYearRate': '+137.11%'}, 'consumePrepayAmount': {'value': 13292.57, 'unit': '元', 'chainComparison': [12822.97], 'chainChangeRate': ['+3.66%'], 'chainLabels': ['上期'], 'yearOverYear': 15362.9, 'yearOverYearRate': '-13.48%'}, 'retentionRate': {'value': 24.9, 'unit': '%', 'chainComparison': [15.64], 'chainChangeRate': ['+59.21%'], 'chainLabels': ['上期'], 'yearOverYear': 13.2, 'yearOverYearRate': '+88.64%'}}
2025-08-01 14:19:53 - request - INFO - log_requests:78 - 请求处理完成: POST http://127.0.0.1:8000/api/query/data/member-charge - 状态码: 200 - 耗时: 0.278秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash_uv) AS total_consume_cash_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_pv查询SQL: SELECT 
        SUM(consume_cash_pv) - SUM(cancel_cash_pv) AS total_consume_cash_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN 20250624 AND 20250630
           AND bid = 3064710828
           ) AS new_user_charger_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.022秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash_uv) AS total_consume_cash_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_pv查询SQL: SELECT 
        SUM(consume_cash_pv) - SUM(cancel_cash_pv) AS total_consume_cash_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN 20250701 AND 20250707
           AND bid = 3064710828
           ) AS new_user_consomer_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.040秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_uv查询SQL: SELECT 
        SUM(consume_cash_uv) AS total_consume_cash_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN 20240701 AND 20240707
           AND bid = 3064710828
           ) AS new_user_charger_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.018秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash_pv) - SUM(cancel_cash_pv) AS total_consume_cash_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_real查询SQL: SELECT 
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash_pv) - SUM(cancel_cash_pv) AS total_consume_cash_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_real查询SQL: SELECT 
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uCardPhoneNum) AS total_card_phone_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.013秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash_uv) AS total_consume_cash_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_pv查询SQL: SELECT 
        SUM(consume_cash_pv) - SUM(cancel_cash_pv) AS total_consume_cash_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uCardPhoneNum) AS total_card_phone_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.023秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN 20250701 AND 20250707
           AND bid = 3064710828
           ) AS new_user_charger_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.022秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_cancel_user
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.065秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:104 - 新增会员查询SQL: SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250624 AND 20250630
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_cancel_user
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.072秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:104 - 新增会员查询SQL: SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250701 AND 20250707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uCardInfoNum) AS total_card_info_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.012秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_uv查询SQL: SELECT 
        SUM(consume_prepay_uv) + SUM(overdue_uv) AS total_prepay_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_uv查询SQL: SELECT 
        SUM(consume_prepay_uv) + SUM(overdue_uv) AS total_prepay_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uCardPhoneNum) AS total_card_phone_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.013秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uCardInfoNum) AS total_card_info_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash_pv) - SUM(cancel_cash_pv) AS total_consume_cash_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.020秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_real查询SQL: SELECT 
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uCardInfoNum) AS total_card_info_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.013秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay_uv) + SUM(overdue_uv) AS total_prepay_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_pv查询SQL: SELECT 
        SUM(consume_prepay_pv) - SUM(cancel_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume0Num) AS total_consume_0_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.021秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume0Num) AS total_consume_0_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_uv查询SQL: SELECT 
        SUM(consume_prepay_uv) + SUM(overdue_uv) AS total_prepay_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay_uv) + SUM(overdue_uv) AS total_prepay_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.025秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_pv查询SQL: SELECT 
        SUM(consume_prepay_pv) - SUM(cancel_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume0Num) AS total_consume_0_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.011秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume1Num) AS total_consume_1_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.013秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume1Num) AS total_consume_1_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay_pv) - SUM(cancel_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.020秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:96 - total_consume_uv查询SQL: 
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN 20250624 AND 20250630
        AND bid = 3064710828
        AND tctype = 2
        
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay_uv) + SUM(overdue_uv) AS total_prepay_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_pv查询SQL: SELECT 
        SUM(consume_prepay_pv) - SUM(cancel_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay_pv) - SUM(cancel_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:96 - total_consume_uv查询SQL: 
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN 20240701 AND 20240707
        AND bid = 3064710828
        AND tctype = 2
        
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume1Num) AS total_consume_1_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume2Num) AS total_consume_2plus_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay_pv) - SUM(cancel_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:96 - total_consume_uv查询SQL: 
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN 20250701 AND 20250707
        AND bid = 3064710828
        AND tctype = 2
        
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume2Num) AS total_consume_2plus_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.021秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN 20250624 AND 20250630
        AND bid = 3064710828
        AND tctype = 2
        
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.022秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:110 - consume_frequency_stats查询SQL: 
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume2Num) AS total_consume_2plus_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uUserInfoNum) AS total_first_info_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:250 - dwoutput数据库查询完成，耗时: 0.374秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:251 - dwoutput数据结果: {'total_all_user_consomer': Decimal('338'), 'total_all_user_charger': Decimal('46'), 'new_user_consomer_total': Decimal('144'), 'new_user_charger_total': Decimal('41'), 'total_card_phone_num': Decimal('33996'), 'total_card_info_num': Decimal('59'), 'total_consume_0_num': Decimal('3274'), 'total_consume_1_num': Decimal('21744'), 'total_consume_2plus_num': Decimal('10070'), 'total_first_info_num': Decimal('3')}
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN 20240701 AND 20240707
        AND bid = 3064710828
        AND tctype = 2
        
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.031秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:110 - consume_frequency_stats查询SQL: 
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uUserInfoNum) AS total_first_info_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.014秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:250 - dwoutput数据库查询完成，耗时: 0.386秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:251 - dwoutput数据结果: {'total_all_user_consomer': Decimal('338'), 'total_all_user_charger': Decimal('47'), 'new_user_consomer_total': Decimal('847'), 'new_user_charger_total': Decimal('54'), 'total_card_phone_num': Decimal('34898'), 'total_card_info_num': Decimal('60'), 'total_consume_0_num': Decimal('3315'), 'total_consume_1_num': Decimal('22463'), 'total_consume_2plus_num': Decimal('10207'), 'total_first_info_num': Decimal('0')}
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:124 - dwoutput消费基础数据查询完成，耗时: 0.375秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:125 - 消费基础数据结果: {'total_consume_amount_virtual': Decimal('3616330'), 'total_consume_pv': Decimal('596'), 'total_consume_cash_real': Decimal('2005352'), 'total_consume_cash_uv': Decimal('320'), 'total_consume_cash_pv': Decimal('327'), 'total_prepay_real': Decimal('1557298'), 'total_prepay_uv': Decimal('237'), 'total_prepay_pv': Decimal('267'), 'total_consume_uv': 516, 'consume_once_members': Decimal('455'), 'consume_twice_members': Decimal('50'), 'consume_thrice_members': Decimal('7'), 'consume_more_than_thrice_members': Decimal('4')}
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uUserInfoNum) AS total_first_info_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_member_consume_data:235 - 会员消费数据获取完成，总耗时: 0.382秒，数据字段数: 19
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:250 - dwoutput数据库查询完成，耗时: 0.386秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:251 - dwoutput数据结果: {'total_all_user_consomer': Decimal('329'), 'total_all_user_charger': Decimal('27'), 'new_user_consomer_total': Decimal('672'), 'new_user_charger_total': Decimal('78'), 'total_card_phone_num': Decimal('15750'), 'total_card_info_num': Decimal('21'), 'total_consume_0_num': Decimal('1276'), 'total_consume_1_num': Decimal('11135'), 'total_consume_2plus_num': Decimal('3722'), 'total_first_info_num': Decimal('0')}
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN 20250701 AND 20250707
        AND bid = 3064710828
        AND tctype = 2
        
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.027秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:110 - consume_frequency_stats查询SQL: 
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:124 - dwoutput消费基础数据查询完成，耗时: 0.399秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:125 - 消费基础数据结果: {'total_consume_amount_virtual': Decimal('7899930'), 'total_consume_pv': Decimal('1341'), 'total_consume_cash_real': Decimal('6212320'), 'total_consume_cash_uv': Decimal('1007'), 'total_consume_cash_pv': Decimal('1041'), 'total_prepay_real': Decimal('1618170'), 'total_prepay_uv': Decimal('248'), 'total_prepay_pv': Decimal('284'), 'total_consume_uv': 1198, 'consume_once_members': Decimal('1072'), 'consume_twice_members': Decimal('104'), 'consume_thrice_members': Decimal('15'), 'consume_more_than_thrice_members': Decimal('7')}
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_member_consume_data:235 - 会员消费数据获取完成，总耗时: 0.404秒，数据字段数: 19
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - 
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.028秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:124 - dwoutput消费基础数据查询完成，耗时: 0.397秒
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:125 - 消费基础数据结果: {'total_consume_amount_virtual': Decimal('7337320'), 'total_consume_pv': Decimal('1226'), 'total_consume_cash_real': Decimal('5281900'), 'total_consume_cash_uv': Decimal('896'), 'total_consume_cash_pv': Decimal('925'), 'total_prepay_real': Decimal('1887720'), 'total_prepay_uv': Decimal('268'), 'total_prepay_pv': Decimal('293'), 'total_consume_uv': 1091, 'consume_once_members': Decimal('972'), 'consume_twice_members': Decimal('98'), 'consume_thrice_members': Decimal('18'), 'consume_more_than_thrice_members': Decimal('3')}
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - _fetch_member_consume_data:235 - 会员消费数据获取完成，总耗时: 0.405秒，数据字段数: 19
2025-08-01 14:19:53 - api.query.MemberConsumeTab - INFO - get_member_consume_data:350 - 会员消费数据查询成功完成 - bid: 3064710828, sid: None
2025-08-01 14:19:53 - request - INFO - log_requests:78 - 请求处理完成: POST http://127.0.0.1:8000/api/query/data/member-consume - 状态码: 200 - 耗时: 0.423秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20240701 AND 20240707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.203秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:106 - 新增会员查询结果: {'new_user_total': Decimal('2')}
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS cancel_user_last_day
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.048秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MIN(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS cancel_user_first_day
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.048秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:120 - wedatas数据库查询完成，耗时: 0.527秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:142 - wedatas数据处理完成: 8个字段
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:143 - 最终结果: {'total_all_user': Decimal('16068'), 'net_all_user': Decimal('16053'), 'total_cancel_user': Decimal('15'), 'new_user_total': Decimal('2'), 'cancel_user_last_day': Decimal('15'), 'cancel_user_first_day': Decimal('15'), 'new_cancel_user': 0.0, 'cancel_user_rate': 0.0}
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:271 - 新增会员数量查询SQL: SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20240701 AND 20240707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20240701 AND 20240707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.058秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:273 - 新增会员数量查询结果: {'new_user_total': Decimal('2')}
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:280 - 新增会员完善率计算完成: 0.0/2.0 = 0.0
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_member_base_data:316 - 会员基础数据获取完成，总耗时: 0.590秒，数据字段数: 22
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250701 AND 20250707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.287秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:106 - 新增会员查询结果: {'new_user_total': Decimal('6')}
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250624 AND 20250630
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.308秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:106 - 新增会员查询结果: {'new_user_total': Decimal('15')}
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS cancel_user_last_day
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.065秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS cancel_user_last_day
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.065秒
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MIN(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS cancel_user_first_day
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.065秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:120 - wedatas数据库查询完成，耗时: 0.724秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:142 - wedatas数据处理完成: 8个字段
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:143 - 最终结果: {'total_all_user': Decimal('35985'), 'net_all_user': Decimal('35960'), 'total_cancel_user': Decimal('25'), 'new_user_total': Decimal('6'), 'cancel_user_last_day': Decimal('25'), 'cancel_user_first_day': Decimal('25'), 'new_cancel_user': 0.0, 'cancel_user_rate': 0.0}
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:271 - 新增会员数量查询SQL: SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250701 AND 20250707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MIN(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS cancel_user_first_day
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.070秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:120 - wedatas数据库查询完成，耗时: 0.743秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:142 - wedatas数据处理完成: 8个字段
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:143 - 最终结果: {'total_all_user': Decimal('35088'), 'net_all_user': Decimal('35063'), 'total_cancel_user': Decimal('25'), 'new_user_total': Decimal('15'), 'cancel_user_last_day': Decimal('25'), 'cancel_user_first_day': Decimal('25'), 'new_cancel_user': 0.0, 'cancel_user_rate': 0.0}
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:271 - 新增会员数量查询SQL: SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250624 AND 20250630
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250701 AND 20250707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.076秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:273 - 新增会员数量查询结果: {'new_user_total': Decimal('6')}
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:280 - 新增会员完善率计算完成: 0.0/6.0 = 0.0
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_member_base_data:316 - 会员基础数据获取完成，总耗时: 0.804秒，数据字段数: 22
2025-08-01 14:19:53 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250624 AND 20250630
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:19:53 - aiomysql - INFO - execute:243 - None
2025-08-01 14:19:53 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.075秒
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:273 - 新增会员数量查询结果: {'new_user_total': Decimal('15')}
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:280 - 新增会员完善率计算完成: 3.0/15.0 = 0.2
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - _fetch_member_base_data:316 - 会员基础数据获取完成，总耗时: 0.824秒，数据字段数: 22
2025-08-01 14:19:53 - api.query.MemberBaseTab - INFO - get_member_base_data:433 - 会员基础数据查询成功完成 - bid: 3064710828, sid: None
2025-08-01 14:19:53 - request - INFO - log_requests:78 - 请求处理完成: POST http://127.0.0.1:8000/api/query/data/member-base - 状态码: 200 - 耗时: 0.835秒
2025-08-01 14:25:00 - core.database - INFO - disconnect:155 - dwoutput数据库连接池已关闭
2025-08-01 14:25:00 - core.database - INFO - disconnect:160 - wedatas数据库连接池已关闭
2025-08-01 14:25:00 - core.database - INFO - disconnect:165 - welife_hydb数据库连接池已关闭
2025-08-01 14:25:00 - core.database - INFO - disconnect:169 - 品质收银数据库连接池已关闭
2025-08-01 14:25:00 - main - INFO - lifespan:33 - 应用已关闭
2025-08-01 14:34:41 - main - INFO - lifespan:23 - 应用启动中...
2025-08-01 14:34:41 - core.database - INFO - connect:26 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-01 14:34:41 - core.database - INFO - connect:40 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-01 14:34:41 - core.database - INFO - connect:43 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-01 14:34:41 - core.database - INFO - connect:57 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-01 14:34:41 - core.database - INFO - connect:60 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-01 14:34:42 - core.database - INFO - connect:74 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-01 14:34:42 - core.database - INFO - connect:77 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-01 14:34:42 - core.database - INFO - connect:88 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-01 14:34:42 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:34:42 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:42 - core.database - INFO - _test_database_connections:105 - dwoutput数据库连接测试成功: (1, 607895813, 'dwoutput')
2025-08-01 14:34:42 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:34:42 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:42 - core.database - INFO - _test_database_connections:112 - wedatas数据库连接测试成功: (1, 339458119, 'wedatas')
2025-08-01 14:34:42 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-01 14:34:42 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:42 - core.database - INFO - _test_database_connections:121 - welife_hydb数据库连接测试成功: (1,)
2025-08-01 14:34:42 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-01 14:34:42 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:42 - core.database - INFO - _test_database_connections:127 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-01 14:34:42 - core.database - INFO - _test_database_connections:138 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-01 14:34:42 - main - INFO - lifespan:27 - 数据库连接已建立
2025-08-01 14:34:51 - request - INFO - log_requests:70 - 收到请求: POST http://127.0.0.1:8000/api/query/data/member-base
2025-08-01 14:34:51 - request - INFO - log_requests:71 - 请求头: {'host': '127.0.0.1:8000', 'connection': 'close', 'content-length': '182', 'x-request-id': 'mdsg8800wz0ce2cq1h', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.q1NGewDxxUAYck01iXXFBZls-pt-zBfmFaFSNwYxSq4', 'sec-ch-ua-platform': '"Windows"', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/json', 'origin': 'http://localhost:5173', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:5173/query', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': '_ga=GA1.1.833588396.1753086687; _ga_R1FN4KJKJH=GS2.1.s1753233947$o2$g0$t1753233947$j60$l0$h0'}
2025-08-01 14:34:51 - request - INFO - log_requests:70 - 收到请求: POST http://127.0.0.1:8000/api/query/data/member-consume
2025-08-01 14:34:51 - request - INFO - log_requests:71 - 请求头: {'host': '127.0.0.1:8000', 'connection': 'close', 'content-length': '182', 'x-request-id': 'mdsg8800cu21b6p3fg9', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.q1NGewDxxUAYck01iXXFBZls-pt-zBfmFaFSNwYxSq4', 'sec-ch-ua-platform': '"Windows"', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/json', 'origin': 'http://localhost:5173', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:5173/query', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': '_ga=GA1.1.833588396.1753086687; _ga_R1FN4KJKJH=GS2.1.s1753233947$o2$g0$t1753233947$j60$l0$h0'}
2025-08-01 14:34:51 - request - INFO - log_requests:70 - 收到请求: POST http://127.0.0.1:8000/api/query/data/member-charge
2025-08-01 14:34:51 - request - INFO - log_requests:71 - 请求头: {'host': '127.0.0.1:8000', 'connection': 'close', 'content-length': '182', 'x-request-id': 'mdsg8800gwtch9wim0d', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.q1NGewDxxUAYck01iXXFBZls-pt-zBfmFaFSNwYxSq4', 'sec-ch-ua-platform': '"Windows"', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/json', 'origin': 'http://localhost:5173', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:5173/query', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': '_ga=GA1.1.833588396.1753086687; _ga_R1FN4KJKJH=GS2.1.s1753233947$o2$g0$t1753233947$j60$l0$h0'}
2025-08-01 14:34:51 - request - INFO - log_requests:70 - 收到请求: POST http://127.0.0.1:8000/api/query/data/coupon-trade
2025-08-01 14:34:51 - request - INFO - log_requests:71 - 请求头: {'host': '127.0.0.1:8000', 'connection': 'close', 'content-length': '182', 'x-request-id': 'mdsg8800smlxnhqra0q', 'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.q1NGewDxxUAYck01iXXFBZls-pt-zBfmFaFSNwYxSq4', 'sec-ch-ua-platform': '"Windows"', 'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"', 'sec-ch-ua-mobile': '?0', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'accept': 'application/json, text/plain, */*', 'content-type': 'application/json', 'origin': 'http://localhost:5173', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:5173/query', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'cookie': '_ga=GA1.1.833588396.1753086687; _ga_R1FN4KJKJH=GS2.1.s1753233947$o2$g0$t1753233947$j60$l0$h0'}
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - get_member_base_data:402 - 收到会员基础数据查询请求 - bid: 3064710828, sid: None, 日期: 2025-07-01~2025-07-07
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_member_base_data:290 - 开始获取会员基础数据 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_member_base_data:290 - 开始获取会员基础数据 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_member_base_data:290 - 开始获取会员基础数据 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:80 - 开始查询wedatas数据库 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:34:51 - api.query.MemberBaseTab - WARNING - _fetch_wedatas_data:81 - 注意：查询日期为 20250701-20250707，请确认这是正确的日期范围！
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:153 - 开始查询dwoutput数据库 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:161 - 累计消费会员查询SQL: SELECT 
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_consomer
        
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:80 - 开始查询wedatas数据库 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:34:51 - api.query.MemberBaseTab - WARNING - _fetch_wedatas_data:81 - 注意：查询日期为 20250624-20250630，请确认这是正确的日期范围！
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:153 - 开始查询dwoutput数据库 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:161 - 累计消费会员查询SQL: SELECT 
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_consomer
        
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:80 - 开始查询wedatas数据库 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:34:51 - api.query.MemberBaseTab - WARNING - _fetch_wedatas_data:81 - 注意：查询日期为 20240701-20240707，请确认这是正确的日期范围！
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:153 - 开始查询dwoutput数据库 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:161 - 累计消费会员查询SQL: SELECT 
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_consomer
        
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - get_member_consume_data:319 - 收到会员消费数据查询请求 - bid: 3064710828, sid: None, 日期: 2025-07-01~2025-07-07
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - get_member_charge_data:248 - 收到会员充值数据查询请求 - bid: 3064710828, sid: None, 日期: 2025-07-01~2025-07-07
2025-08-01 14:34:51 - api.query.CouponTradeTab - INFO - get_coupon_trade_data:21 - 收到券交易数据查询请求 - bid: 3064710828, sid: None, 查询类型: week
2025-08-01 14:34:51 - api.query.CouponTradeTab - INFO - get_coupon_trade_data:25 - 原始传入日期: 2025-07-01 ~ 2025-07-07
2025-08-01 14:34:51 - api.query.CouponTradeTab - INFO - get_coupon_trade_data:26 - 实际查询日期: 2025-07-01 ~ 2025-07-07
2025-08-01 14:34:51 - api.query.CouponTradeTab - INFO - get_coupon_trade_data:29 - 开始查询券交易数据（仅当前期间）
2025-08-01 14:34:51 - api.query.CouponTradeTab - INFO - _get_coupon_data:58 - 开始查询wedatas券交易数据 - bid: 3064710828, sid: None, 时间范围: 2025-07-01-2025-07-07
2025-08-01 14:34:51 - api.query.CouponTradeTab - INFO - _get_coupon_data:112 - 执行SQL: 
            SELECT
                couponname,
                couponid,
                
                -- 券发放量（发放 - 取消）
                SUM(coupon_send) - SUM(cancel_coupon_send) AS coupon_sent,

                -- 券使用量（使用 - 取消）
                SUM(coupon_used) - SUM(cancel_coupon_used) AS coupon_used,

                -- 使用率 = 使用量 / 发放量（防止除以0）
                ROUND(
                    (SUM(coupon_used) - SUM(cancel_coupon_used)) / 
                    NULLIF(SUM(coupon_send) - SUM(cancel_coupon_send), 0),
                    4
                ) AS coupon_use_rate,

                -- 券抵扣金额 = 使用量 × 单张金额 camount
                SUM((coupon_used - cancel_coupon_used) * camount) AS coupon_discount_amount,

                -- 带动储值消费（储值 - 取消）
                SUM(trade_prepay) - SUM(cancel_trade_prepay) AS trade_prepay_amount,

                -- 带动现金消费（现金 - 取消）
                SUM(trade_cash) - SUM(cancel_trade_cash) AS trade_cash_amount,

                -- 总交易金额（总额 - 取消）
                SUM(trade_amount) - SUM(cancel_trade_amount) AS trade_total_amount

            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN %s AND %s
                AND bid = %s
            
            GROUP BY couponname, couponid
            ORDER BY (SUM(trade_cash) - SUM(cancel_trade_cash)) DESC
            
2025-08-01 14:34:51 - api.query.CouponTradeTab - INFO - _get_coupon_data:113 - 参数: ['20250701', '20250707', '3064710828']
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_member_consume_data:202 - 开始获取会员消费数据 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_member_consume_data:202 - 开始获取会员消费数据 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_member_consume_data:202 - 开始获取会员消费数据 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_member_charge_data:154 - 开始获取会员充值数据 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_member_charge_data:154 - 开始获取会员充值数据 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_member_charge_data:154 - 开始获取会员充值数据 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:63 - 开始查询dwoutput消费基础数据 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_amount_virtual查询SQL: SELECT 
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:136 - 开始查询dwoutput储值使用详情 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:149 - 储值使用详情查询SQL: 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250701 AND 20250707 AND bid = 3064710828 )
        ) AS total_prepay_used_real
        
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:169 - 开始查询wedatas券交易数据 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:182 - 券交易查询SQL: 
            SELECT 
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:63 - 开始查询dwoutput消费基础数据 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_amount_virtual查询SQL: SELECT 
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:136 - 开始查询dwoutput储值使用详情 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:149 - 储值使用详情查询SQL: 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250623 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250624 AND 20250630 AND bid = 3064710828 )
        ) AS total_prepay_used_real
        
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:169 - 开始查询wedatas券交易数据 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:182 - 券交易查询SQL: 
            SELECT 
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:63 - 开始查询dwoutput消费基础数据 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_amount_virtual查询SQL: SELECT 
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:136 - 开始查询dwoutput储值使用详情 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:149 - 储值使用详情查询SQL: 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240701 AND 20240707 AND bid = 3064710828 )
        ) AS total_prepay_used_real
        
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:169 - 开始查询wedatas券交易数据 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:182 - 券交易查询SQL: 
            SELECT 
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:62 - 开始查询dwoutput充值基础数据 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:132 - 开始查询dwoutput储值消耗详情 - bid: 3064710828, sid: None, 时间范围: 20250701-20250707
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:138 - 储值消耗详情查询SQL: 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250701 AND 20250707 AND bid = 3064710828 )
        ) AS total_consume_prepay_used
        
            
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:62 - 开始查询dwoutput充值基础数据 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:132 - 开始查询dwoutput储值消耗详情 - bid: 3064710828, sid: None, 时间范围: 20250624-20250630
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:138 - 储值消耗详情查询SQL: 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250623 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250624 AND 20250630 AND bid = 3064710828 )
        ) AS total_consume_prepay_used
        
            
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:62 - 开始查询dwoutput充值基础数据 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:132 - 开始查询dwoutput储值消耗详情 - bid: 3064710828, sid: None, 时间范围: 20240701-20240707
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:138 - 储值消耗详情查询SQL: 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240701 AND 20240707 AND bid = 3064710828 )
        ) AS total_consume_prepay_used
        
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT
                couponname,
                couponid,
                
                -- 券发放量（发放 - 取消）
                SUM(coupon_send) - SUM(cancel_coupon_send) AS coupon_sent,

                -- 券使用量（使用 - 取消）
                SUM(coupon_used) - SUM(cancel_coupon_used) AS coupon_used,

                -- 使用率 = 使用量 / 发放量（防止除以0）
                ROUND(
                    (SUM(coupon_used) - SUM(cancel_coupon_used)) / 
                    NULLIF(SUM(coupon_send) - SUM(cancel_coupon_send), 0),
                    4
                ) AS coupon_use_rate,

                -- 券抵扣金额 = 使用量 × 单张金额 camount
                SUM((coupon_used - cancel_coupon_used) * camount) AS coupon_discount_amount,

                -- 带动储值消费（储值 - 取消）
                SUM(trade_prepay) - SUM(cancel_trade_prepay) AS trade_prepay_amount,

                -- 带动现金消费（现金 - 取消）
                SUM(trade_cash) - SUM(cancel_trade_cash) AS trade_cash_amount,

                -- 总交易金额（总额 - 取消）
                SUM(trade_amount) - SUM(cancel_trade_amount) AS trade_total_amount

            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN '20250701' AND '20250707'
                AND bid = '3064710828'
            
            GROUP BY couponname, couponid
            ORDER BY (SUM(trade_cash) - SUM(cancel_trade_cash)) DESC
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - ['20250701', '20250707', '3064710828']
2025-08-01 14:34:51 - api.query.CouponTradeTab - INFO - _get_coupon_data:121 - wedatas券交易数据查询完成，耗时: 0.019秒，返回8条记录
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.018秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.030秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_consomer
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.040秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_consomer
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.040秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_consomer
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.044秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250701 AND 20250707 AND bid = 3064710828 )
        ) AS total_prepay_used_real
        
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.036秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user + cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.050秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user + cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.067秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user + cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.066秒
2025-08-01 14:34:51 - api.query.CouponTradeTab - INFO - get_coupon_trade_data:34 - 券交易数据查询完成，总耗时: 0.065秒
2025-08-01 14:34:51 - api.query.CouponTradeTab - INFO - get_coupon_trade_data:40 - 券交易数据处理成功 - bid: 3064710828, sid: None
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:186 - wedatas券交易数据查询完成，耗时: 0.062秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:187 - 券交易数据结果: {'total_coupon_trade_amount': Decimal('349210')}
2025-08-01 14:34:51 - request - INFO - log_requests:78 - 请求处理完成: POST http://127.0.0.1:8000/api/query/data/coupon-trade - 状态码: 200 - 耗时: 0.081秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250701 AND 20250707
              AND bid = 3064710828
              
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.067秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.070秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:186 - wedatas券交易数据查询完成，耗时: 0.071秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:187 - 券交易数据结果: {'total_coupon_trade_amount': Decimal('616510')}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.075秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(trade_amount) - SUM(cancel_trade_amount) AS total_coupon_trade_amount
        
            FROM wedatas_welife_coupon_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.075秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:186 - wedatas券交易数据查询完成，耗时: 0.077秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_wedatas_coupon_trade_data:187 - 券交易数据结果: {'total_coupon_trade_amount': Decimal('238120')}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_amount) + SUM(overdue_amount) - SUM(cancel_amount) AS total_consume_amount_virtual
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.076秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250623 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250624 AND 20250630 AND bid = 3064710828 )
        ) AS total_prepay_used_real
        
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.086秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250701 AND 20250707 AND bid = 3064710828 )
        ) AS total_consume_prepay_used
        
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.084秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240701 AND 20240707 AND bid = 3064710828 )
        ) AS total_prepay_used_real
        
            FROM dprpt_welife_trade_consume_detail
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.099秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_pv查询SQL: SELECT 
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:153 - dwoutput储值使用详情查询完成，耗时: 0.104秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:154 - 储值使用详情结果: {'total_prepay_used_real': Decimal('1329257')}
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_pv查询SQL: SELECT 
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_pv查询SQL: SELECT 
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:153 - dwoutput储值使用详情查询完成，耗时: 0.103秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:154 - 储值使用详情结果: {'total_prepay_used_real': Decimal('1282297')}
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:142 - dwoutput储值消耗详情查询完成，耗时: 0.101秒
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:143 - 储值消耗详情结果: {'total_consume_prepay_used': Decimal('1329257')}
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:153 - dwoutput储值使用详情查询完成，耗时: 0.103秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_detail_data:154 - 储值使用详情结果: {'total_prepay_used_real': Decimal('1536290')}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS net_all_user
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.047秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250624 AND 20250630
              AND bid = 3064710828
              
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.110秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250623 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20250630 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20250624 AND 20250630 AND bid = 3064710828 )
        ) AS total_consume_prepay_used
        
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.111秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        (
          -- 前一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240630 AND bid = 3064710828 )
          -
          -- 最后一天的沉淀金额
          (SELECT COALESCE(SUM(charge_cash_unused), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime = 20240707 AND bid = 3064710828 )
          +
          -- 期间的实收净充值
          (SELECT COALESCE(SUM(charge_cash - cancel_charge_cash), 0)
           FROM dprpt_welife_charge_log_bid
           WHERE ftime BETWEEN 20240701 AND 20240707 AND bid = 3064710828 )
        ) AS total_consume_prepay_used
        
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.112秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_amount_pv) - SUM(cancel_charge_amount_pv) AS total_charge_pv
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20240701 AND 20240707
              AND bid = 3064710828
              
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.118秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.025秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS net_all_user
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.073秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS net_all_user
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.074秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_charger
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.040秒
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:142 - dwoutput储值消耗详情查询完成，耗时: 0.138秒
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:143 - 储值消耗详情结果: {'total_consume_prepay_used': Decimal('1282297')}
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:142 - dwoutput储值消耗详情查询完成，耗时: 0.137秒
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_consume_detail_data:143 - 储值消耗详情结果: {'total_consume_prepay_used': Decimal('1536290')}
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_real查询SQL: SELECT 
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_charger
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.044秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250701 AND 20250707
              AND bid = 3064710828
              
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.053秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20240701 AND 20240707
              AND bid = 3064710828
              
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_cancel_user
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.051秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:104 - 新增会员查询SQL: SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20240701 AND 20240707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.058秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_cash) - SUM(cancel_charge_cash) AS total_charge_cash
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250624 AND 20250630
              AND bid = 3064710828
              
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.020秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_amount_pv) + SUM(overdue_pv) - SUM(cancel_amount_pv) AS total_consume_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.062秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN 20240701 AND 20240707
           AND bid = 3064710828
           ) AS new_user_consomer_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.025秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_real查询SQL: SELECT 
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_uv查询SQL: SELECT 
        SUM(consume_cash_uv) AS total_consume_cash_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_real查询SQL: SELECT 
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(all_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_user_log
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_all_user_charger
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.086秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250701 AND 20250707
              AND bid = 3064710828
              
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20240701 AND 20240707
              AND bid = 3064710828
              
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN 20250624 AND 20250630
           AND bid = 3064710828
           ) AS new_user_consomer_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.057秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.021秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_uv查询SQL: SELECT 
        SUM(consume_cash_uv) AS total_consume_cash_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash) - SUM(cancel_cash) AS total_consume_cash_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.023秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN 20240701 AND 20240707
           AND bid = 3064710828
           ) AS new_user_charger_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.023秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_uv查询SQL: SELECT 
        SUM(consume_cash_uv) AS total_consume_cash_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash_uv) AS total_consume_cash_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.025秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_amount) - SUM(cancel_charge_amount) AS total_charge_amount
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250624 AND 20250630
              AND bid = 3064710828
              
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.026秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_cancel_user
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.075秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user_consomer)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN 20250701 AND 20250707
           AND bid = 3064710828
           ) AS new_user_consomer_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.021秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_pv查询SQL: SELECT 
        SUM(consume_cash_pv) AS total_consume_cash_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:104 - 新增会员查询SQL: SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250701 AND 20250707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS total_cancel_user
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.076秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:104 - 新增会员查询SQL: SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250624 AND 20250630
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250701 AND 20250707
              AND bid = 3064710828
              
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.014秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20240701 AND 20240707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.053秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:106 - 新增会员查询结果: {'new_user_total': Decimal('2')}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20240701 AND 20240707
              AND bid = 3064710828
              
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uCardPhoneNum) AS total_card_phone_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.012秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN 20250624 AND 20250630
           AND bid = 3064710828
           ) AS new_user_charger_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(charge_present) - SUM(cancel_charge_present) AS total_charge_present
        
            FROM dprpt_welife_charge_log_bid
            WHERE ftime BETWEEN 20250624 AND 20250630
              AND bid = 3064710828
              
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.013秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash_uv) AS total_consume_cash_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_pv查询SQL: SELECT 
        SUM(consume_cash_pv) AS total_consume_cash_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash_uv) AS total_consume_cash_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.020秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_consume_cash_pv查询SQL: SELECT 
        SUM(consume_cash_pv) AS total_consume_cash_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20250707
         AND bid = 3064710828
         ) AS total_charge_amount_unused
        
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:120 - dwoutput充值基础数据查询完成，耗时: 0.226秒
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:121 - 充值基础数据结果: {'total_charge_pv': Decimal('104'), 'total_charge_cash': Decimal('1770000'), 'total_charge_amount': Decimal('2172000'), 'total_charge_present': Decimal('402000'), 'total_charge_amount_unused': Decimal('18292906')}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user_charger)
         FROM dprpt_welife_user_log
         WHERE ftime BETWEEN 20250701 AND 20250707
           AND bid = 3064710828
           ) AS new_user_charger_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.018秒
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_member_charge_data:178 - 会员充值数据获取完成，总耗时: 0.233秒，数据字段数: 7
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash_pv) AS total_consume_cash_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.021秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_real查询SQL: SELECT 
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20240707
         AND bid = 3064710828
         ) AS total_charge_amount_unused
        
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:120 - dwoutput充值基础数据查询完成，耗时: 0.229秒
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:121 - 充值基础数据结果: {'total_charge_pv': Decimal('134'), 'total_charge_cash': Decimal('1770000'), 'total_charge_amount': Decimal('2170000'), 'total_charge_present': Decimal('400000'), 'total_charge_amount_unused': Decimal('7715040')}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uCardInfoNum) AS total_card_info_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.014秒
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_member_charge_data:178 - 会员充值数据获取完成，总耗时: 0.238秒，数据字段数: 7
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uCardPhoneNum) AS total_card_phone_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.014秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        (SELECT SUM(charge_amount_unused)
         FROM dprpt_welife_charge_log_bid
         WHERE ftime = 20250630
         AND bid = 3064710828
         ) AS total_charge_amount_unused
        
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.012秒
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:120 - dwoutput充值基础数据查询完成，耗时: 0.233秒
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_dwoutput_charge_base_data:121 - 充值基础数据结果: {'total_charge_pv': Decimal('94'), 'total_charge_cash': Decimal('1520000'), 'total_charge_amount': Decimal('1854000'), 'total_charge_present': Decimal('334000'), 'total_charge_amount_unused': Decimal('17739076')}
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - _fetch_member_charge_data:178 - 会员充值数据获取完成，总耗时: 0.241秒，数据字段数: 7
2025-08-01 14:34:51 - api.query.MemberChargeTab - INFO - get_member_charge_data:279 - 会员充值数据查询成功完成 - bid: 3064710828, sid: None
2025-08-01 14:34:51 - api.query.MemberDataQuery - INFO - get_member_charge_data:161 - 会员充值数据原始对象: charge_count=FieldDataModel(value=104, unit='笔', chain_comparison=[94], chain_change_rate=['+10.64%'], chain_labels=['上期'], year_over_year=134, year_over_year_rate='-22.39%') charge_amount=FieldDataModel(value=17700.0, unit='元', chain_comparison=[15200.0], chain_change_rate=['+16.45%'], chain_labels=['上期'], year_over_year=17700.0, year_over_year_rate='+0.00%') period_charge_amount=FieldDataModel(value=21720.0, unit='元', chain_comparison=[18540.0], chain_change_rate=['+17.15%'], chain_labels=['上期'], year_over_year=21700.0, year_over_year_rate='+0.09%') period_charge_present=FieldDataModel(value=4020.0, unit='元', chain_comparison=[3340.0], chain_change_rate=['+20.36%'], chain_labels=['上期'], year_over_year=4000.0, year_over_year_rate='+0.50%') period_charge_amount_unused=FieldDataModel(value=182929.06, unit='元', chain_comparison=[177390.76], chain_change_rate=['+3.12%'], chain_labels=['上期'], year_over_year=77150.4, year_over_year_rate='+137.11%') consume_prepay_amount=FieldDataModel(value=13292.57, unit='元', chain_comparison=[12822.97], chain_change_rate=['+3.66%'], chain_labels=['上期'], year_over_year=15362.9, year_over_year_rate='-13.48%') retention_rate=FieldDataModel(value=24.9, unit='%', chain_comparison=[15.64], chain_change_rate=['+59.21%'], chain_labels=['上期'], year_over_year=13.2, year_over_year_rate='+88.64%')
2025-08-01 14:34:51 - api.query.MemberDataQuery - INFO - get_member_charge_data:165 - 会员充值数据转换后: {'chargeCount': {'value': 104, 'unit': '笔', 'chainComparison': [94], 'chainChangeRate': ['+10.64%'], 'chainLabels': ['上期'], 'yearOverYear': 134, 'yearOverYearRate': '-22.39%'}, 'chargeAmount': {'value': 17700.0, 'unit': '元', 'chainComparison': [15200.0], 'chainChangeRate': ['+16.45%'], 'chainLabels': ['上期'], 'yearOverYear': 17700.0, 'yearOverYearRate': '+0.00%'}, 'periodChargeAmount': {'value': 21720.0, 'unit': '元', 'chainComparison': [18540.0], 'chainChangeRate': ['+17.15%'], 'chainLabels': ['上期'], 'yearOverYear': 21700.0, 'yearOverYearRate': '+0.09%'}, 'periodChargePresent': {'value': 4020.0, 'unit': '元', 'chainComparison': [3340.0], 'chainChangeRate': ['+20.36%'], 'chainLabels': ['上期'], 'yearOverYear': 4000.0, 'yearOverYearRate': '+0.50%'}, 'periodChargeAmountUnused': {'value': 182929.06, 'unit': '元', 'chainComparison': [177390.76], 'chainChangeRate': ['+3.12%'], 'chainLabels': ['上期'], 'yearOverYear': 77150.4, 'yearOverYearRate': '+137.11%'}, 'consumePrepayAmount': {'value': 13292.57, 'unit': '元', 'chainComparison': [12822.97], 'chainChangeRate': ['+3.66%'], 'chainLabels': ['上期'], 'yearOverYear': 15362.9, 'yearOverYearRate': '-13.48%'}, 'retentionRate': {'value': 24.9, 'unit': '%', 'chainComparison': [15.64], 'chainChangeRate': ['+59.21%'], 'chainLabels': ['上期'], 'yearOverYear': 13.2, 'yearOverYearRate': '+88.64%'}}
2025-08-01 14:34:51 - request - INFO - log_requests:78 - 请求处理完成: POST http://127.0.0.1:8000/api/query/data/member-charge - 状态码: 200 - 耗时: 0.260秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash_pv) AS total_consume_cash_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_real查询SQL: SELECT 
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uCardPhoneNum) AS total_card_phone_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_uv查询SQL: SELECT 
        SUM(consume_prepay_uv) + SUM(overdue_uv) AS total_prepay_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_cash_pv) AS total_consume_cash_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.022秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_real查询SQL: SELECT 
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume0Num) AS total_consume_0_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uCardInfoNum) AS total_card_info_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_uv查询SQL: SELECT 
        SUM(consume_prepay_uv) + SUM(overdue_uv) AS total_prepay_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uCardInfoNum) AS total_card_info_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay_uv) + SUM(overdue_uv) AS total_prepay_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_pv查询SQL: SELECT 
        SUM(consume_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume0Num) AS total_consume_0_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.013秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS cancel_user_last_day
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.052秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume1Num) AS total_consume_1_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay) - SUM(cancel_prepay) + SUM(overdue_amount) AS total_prepay_real
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.020秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_uv查询SQL: SELECT 
        SUM(consume_prepay_uv) + SUM(overdue_uv) AS total_prepay_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume0Num) AS total_consume_0_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume1Num) AS total_consume_1_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.013秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay_uv) + SUM(overdue_uv) AS total_prepay_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.021秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_pv查询SQL: SELECT 
        SUM(consume_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume2Num) AS total_consume_2plus_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.014秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:96 - total_consume_uv查询SQL: 
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN 20250701 AND 20250707
        AND bid = 3064710828
        AND tctype = 2
        
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay_uv) + SUM(overdue_uv) AS total_prepay_uv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.016秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:90 - total_prepay_pv查询SQL: SELECT 
        SUM(consume_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250701 AND 20250707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.079秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:106 - 新增会员查询结果: {'new_user_total': Decimal('6')}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250624 AND 20250630
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.080秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:106 - 新增会员查询结果: {'new_user_total': Decimal('15')}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume1Num) AS total_consume_1_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.013秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume2Num) AS total_consume_2plus_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.014秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN 20250701 AND 20250707
        AND bid = 3064710828
        AND tctype = 2
        
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.018秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:96 - total_consume_uv查询SQL: 
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN 20240701 AND 20240707
        AND bid = 3064710828
        AND tctype = 2
        
        
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:110 - consume_frequency_stats查询SQL: 
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uUserInfoNum) AS total_first_info_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:250 - dwoutput数据库查询完成，耗时: 0.313秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:251 - dwoutput数据结果: {'total_all_user_consomer': Decimal('329'), 'total_all_user_charger': Decimal('27'), 'new_user_consomer_total': Decimal('672'), 'new_user_charger_total': Decimal('78'), 'total_card_phone_num': Decimal('15750'), 'total_card_info_num': Decimal('21'), 'total_consume_0_num': Decimal('1276'), 'total_consume_1_num': Decimal('11135'), 'total_consume_2plus_num': Decimal('3722'), 'total_first_info_num': Decimal('0')}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        SUM(consume_prepay_pv) + SUM(overdue_pv) AS total_prepay_pv
         
            FROM dprpt_welife_consume_log
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:96 - total_consume_uv查询SQL: 
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN 20250624 AND 20250630
        AND bid = 3064710828
        AND tctype = 2
        
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uConsume2Num) AS total_consume_2plus_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime = (
        SELECT MAX(ftime)
        FROM dprpt_welife_users_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.014秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uUserInfoNum) AS total_first_info_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.015秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:250 - dwoutput数据库查询完成，耗时: 0.321秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:251 - dwoutput数据结果: {'total_all_user_consomer': Decimal('338'), 'total_all_user_charger': Decimal('46'), 'new_user_consomer_total': Decimal('144'), 'new_user_charger_total': Decimal('41'), 'total_card_phone_num': Decimal('33996'), 'total_card_info_num': Decimal('59'), 'total_consume_0_num': Decimal('3274'), 'total_consume_1_num': Decimal('21744'), 'total_consume_2plus_num': Decimal('10070'), 'total_first_info_num': Decimal('3')}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MIN(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20240701 AND 20240707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS cancel_user_first_day
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.046秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:120 - wedatas数据库查询完成，耗时: 0.323秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:142 - wedatas数据处理完成: 8个字段
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:143 - 最终结果: {'total_all_user': Decimal('16068'), 'net_all_user': Decimal('16053'), 'total_cancel_user': Decimal('15'), 'new_user_total': Decimal('2'), 'cancel_user_last_day': Decimal('15'), 'cancel_user_first_day': Decimal('15'), 'new_cancel_user': 0.0, 'cancel_user_rate': 0.0}
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:271 - 新增会员数量查询SQL: SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20240701 AND 20240707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN 20240701 AND 20240707
        AND bid = 3064710828
        AND tctype = 2
        
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:110 - consume_frequency_stats查询SQL: 
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.019秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:124 - dwoutput消费基础数据查询完成，耗时: 0.325秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:125 - 消费基础数据结果: {'total_consume_amount_virtual': Decimal('7899930'), 'total_consume_pv': Decimal('1341'), 'total_consume_cash_real': Decimal('6212320'), 'total_consume_cash_uv': Decimal('1007'), 'total_consume_cash_pv': Decimal('1050'), 'total_prepay_real': Decimal('1618170'), 'total_prepay_uv': Decimal('248'), 'total_prepay_pv': Decimal('291'), 'total_consume_uv': 1198, 'consume_once_members': Decimal('1072'), 'consume_twice_members': Decimal('104'), 'consume_thrice_members': Decimal('15'), 'consume_more_than_thrice_members': Decimal('7')}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
        SELECT COUNT(DISTINCT uno) AS total_consume_uv
        FROM dprpt_welife_trade_consume_detail
        WHERE ftime BETWEEN 20250624 AND 20250630
        AND bid = 3064710828
        AND tctype = 2
        
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.017秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_member_consume_data:235 - 会员消费数据获取完成，总耗时: 0.328秒，数据字段数: 19
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:110 - consume_frequency_stats查询SQL: 
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
            SELECT 
        SUM(uUserInfoNum) AS total_first_info_num
        
            FROM dprpt_welife_users_stat
            WHERE ftime BETWEEN 20250701 AND 20250707
            AND bid = 3064710828
            
            
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.018秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:250 - dwoutput数据库查询完成，耗时: 0.338秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_dwoutput_data:251 - dwoutput数据结果: {'total_all_user_consomer': Decimal('338'), 'total_all_user_charger': Decimal('47'), 'new_user_consomer_total': Decimal('847'), 'new_user_charger_total': Decimal('54'), 'total_card_phone_num': Decimal('34898'), 'total_card_info_num': Decimal('60'), 'total_consume_0_num': Decimal('3315'), 'total_consume_1_num': Decimal('22463'), 'total_consume_2plus_num': Decimal('10207'), 'total_first_info_num': Decimal('0')}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN 20240701 AND 20240707
            AND bid = 3064710828
            
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.018秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:124 - dwoutput消费基础数据查询完成，耗时: 0.337秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:125 - 消费基础数据结果: {'total_consume_amount_virtual': Decimal('7337320'), 'total_consume_pv': Decimal('1226'), 'total_consume_cash_real': Decimal('5281900'), 'total_consume_cash_uv': Decimal('896'), 'total_consume_cash_pv': Decimal('931'), 'total_prepay_real': Decimal('1887720'), 'total_prepay_uv': Decimal('268'), 'total_prepay_pv': Decimal('296'), 'total_consume_uv': 1091, 'consume_once_members': Decimal('972'), 'consume_twice_members': Decimal('98'), 'consume_thrice_members': Decimal('18'), 'consume_more_than_thrice_members': Decimal('3')}
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_member_consume_data:235 - 会员消费数据获取完成，总耗时: 0.341秒，数据字段数: 19
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - 
        SELECT
          SUM(CASE WHEN consume_count = 1 THEN 1 ELSE 0 END) AS consume_once_members,
          SUM(CASE WHEN consume_count = 2 THEN 1 ELSE 0 END) AS consume_twice_members,
          SUM(CASE WHEN consume_count = 3 THEN 1 ELSE 0 END) AS consume_thrice_members,
          SUM(CASE WHEN consume_count > 3 THEN 1 ELSE 0 END) AS consume_more_than_thrice_members
        FROM (
          SELECT uno, COUNT(*) AS consume_count
          FROM dprpt_welife_trade_consume_detail
          WHERE ftime BETWEEN 20250624 AND 20250630
            AND bid = 3064710828
            
            AND tctype = 2
          GROUP BY uno
        ) AS member_consume_stats
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_dwoutput_one:261 - dwoutput单条查询完成，结果: 有数据，耗时0.018秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:124 - dwoutput消费基础数据查询完成，耗时: 0.342秒
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_dwoutput_consume_base_data:125 - 消费基础数据结果: {'total_consume_amount_virtual': Decimal('3616330'), 'total_consume_pv': Decimal('596'), 'total_consume_cash_real': Decimal('2005352'), 'total_consume_cash_uv': Decimal('320'), 'total_consume_cash_pv': Decimal('330'), 'total_prepay_real': Decimal('1557298'), 'total_prepay_uv': Decimal('237'), 'total_prepay_pv': Decimal('270'), 'total_consume_uv': 516, 'consume_once_members': Decimal('455'), 'consume_twice_members': Decimal('50'), 'consume_thrice_members': Decimal('7'), 'consume_more_than_thrice_members': Decimal('4')}
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - _fetch_member_consume_data:235 - 会员消费数据获取完成，总耗时: 0.347秒，数据字段数: 19
2025-08-01 14:34:51 - api.query.MemberConsumeTab - INFO - get_member_consume_data:350 - 会员消费数据查询成功完成 - bid: 3064710828, sid: None
2025-08-01 14:34:51 - request - INFO - log_requests:78 - 请求处理完成: POST http://127.0.0.1:8000/api/query/data/member-consume - 状态码: 200 - 耗时: 0.364秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS cancel_user_last_day
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.052秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MAX(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS cancel_user_last_day
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.065秒
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20240701 AND 20240707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.057秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:273 - 新增会员数量查询结果: {'new_user_total': Decimal('2')}
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:280 - 新增会员完善率计算完成: 0.0/2.0 = 0.0
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_member_base_data:316 - 会员基础数据获取完成，总耗时: 0.387秒，数据字段数: 22
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MIN(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250701 AND 20250707
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS cancel_user_first_day
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.080秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:120 - wedatas数据库查询完成，耗时: 0.436秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:142 - wedatas数据处理完成: 8个字段
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:143 - 最终结果: {'total_all_user': Decimal('35985'), 'net_all_user': Decimal('35960'), 'total_cancel_user': Decimal('25'), 'new_user_total': Decimal('6'), 'cancel_user_last_day': Decimal('25'), 'cancel_user_first_day': Decimal('25'), 'new_cancel_user': 0.0, 'cancel_user_rate': 0.0}
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(cancel_card_member_total)
         FROM wedatas_user_card_category_stat
         WHERE ftime = (
        SELECT MIN(ftime)
        FROM wedatas_user_card_category_stat
        WHERE ftime BETWEEN 20250624 AND 20250630
          AND bid = 3064710828
          
        )
         AND bid = 3064710828
         ) AS cancel_user_first_day
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.066秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:271 - 新增会员数量查询SQL: SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250701 AND 20250707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:120 - wedatas数据库查询完成，耗时: 0.436秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:142 - wedatas数据处理完成: 8个字段
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_wedatas_data:143 - 最终结果: {'total_all_user': Decimal('35088'), 'net_all_user': Decimal('35063'), 'total_cancel_user': Decimal('25'), 'new_user_total': Decimal('15'), 'cancel_user_last_day': Decimal('25'), 'cancel_user_first_day': Decimal('25'), 'new_cancel_user': 0.0, 'cancel_user_rate': 0.0}
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:271 - 新增会员数量查询SQL: SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250624 AND 20250630
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250701 AND 20250707
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.075秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:273 - 新增会员数量查询结果: {'new_user_total': Decimal('6')}
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:280 - 新增会员完善率计算完成: 0.0/6.0 = 0.0
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_member_base_data:316 - 会员基础数据获取完成，总耗时: 0.513秒，数据字段数: 22
2025-08-01 14:34:51 - aiomysql - INFO - execute:242 - SELECT 
        (SELECT SUM(new_user)
         FROM wedatas_user_card_category_stat
         WHERE ftime BETWEEN 20250624 AND 20250630
           AND bid = 3064710828
           ) AS new_user_total
        
2025-08-01 14:34:51 - aiomysql - INFO - execute:243 - None
2025-08-01 14:34:51 - core.database - INFO - execute_wedatas_one:301 - wedatas单条查询完成，结果: 有数据，耗时0.077秒
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:273 - 新增会员数量查询结果: {'new_user_total': Decimal('15')}
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _calculate_new_user_info_rate:280 - 新增会员完善率计算完成: 3.0/15.0 = 0.2
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - _fetch_member_base_data:316 - 会员基础数据获取完成，总耗时: 0.519秒，数据字段数: 22
2025-08-01 14:34:51 - api.query.MemberBaseTab - INFO - get_member_base_data:433 - 会员基础数据查询成功完成 - bid: 3064710828, sid: None
2025-08-01 14:34:51 - request - INFO - log_requests:78 - 请求处理完成: POST http://127.0.0.1:8000/api/query/data/member-base - 状态码: 200 - 耗时: 0.529秒
2025-08-01 14:36:19 - core.database - INFO - disconnect:155 - dwoutput数据库连接池已关闭
2025-08-01 14:36:19 - core.database - INFO - disconnect:160 - wedatas数据库连接池已关闭
2025-08-01 14:36:19 - core.database - INFO - disconnect:165 - welife_hydb数据库连接池已关闭
2025-08-01 14:36:19 - core.database - INFO - disconnect:169 - 品质收银数据库连接池已关闭
2025-08-01 14:36:19 - main - INFO - lifespan:33 - 应用已关闭
2025-08-01 14:36:22 - main - INFO - lifespan:23 - 应用启动中...
2025-08-01 14:36:22 - core.database - INFO - connect:26 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-01 14:36:22 - core.database - INFO - connect:40 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-01 14:36:22 - core.database - INFO - connect:43 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-01 14:36:22 - core.database - INFO - connect:57 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-01 14:36:22 - core.database - INFO - connect:60 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-01 14:36:23 - core.database - INFO - connect:74 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-01 14:36:23 - core.database - INFO - connect:77 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-01 14:36:23 - core.database - INFO - connect:88 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-01 14:36:23 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:36:23 - aiomysql - INFO - execute:243 - None
2025-08-01 14:36:23 - core.database - INFO - _test_database_connections:105 - dwoutput数据库连接测试成功: (1, 339460697, 'dwoutput')
2025-08-01 14:36:23 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:36:23 - aiomysql - INFO - execute:243 - None
2025-08-01 14:36:23 - core.database - INFO - _test_database_connections:112 - wedatas数据库连接测试成功: (1, 607898407, 'wedatas')
2025-08-01 14:36:23 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-01 14:36:23 - aiomysql - INFO - execute:243 - None
2025-08-01 14:36:23 - core.database - INFO - _test_database_connections:121 - welife_hydb数据库连接测试成功: (1,)
2025-08-01 14:36:23 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-01 14:36:23 - aiomysql - INFO - execute:243 - None
2025-08-01 14:36:23 - core.database - INFO - _test_database_connections:127 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-01 14:36:23 - core.database - INFO - _test_database_connections:138 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-01 14:36:23 - main - INFO - lifespan:27 - 数据库连接已建立
2025-08-01 14:36:38 - core.database - INFO - disconnect:155 - dwoutput数据库连接池已关闭
2025-08-01 14:36:38 - core.database - INFO - disconnect:160 - wedatas数据库连接池已关闭
2025-08-01 14:36:38 - core.database - INFO - disconnect:165 - welife_hydb数据库连接池已关闭
2025-08-01 14:36:38 - core.database - INFO - disconnect:169 - 品质收银数据库连接池已关闭
2025-08-01 14:36:38 - main - INFO - lifespan:33 - 应用已关闭
2025-08-01 14:36:41 - main - INFO - lifespan:23 - 应用启动中...
2025-08-01 14:36:41 - core.database - INFO - connect:26 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-01 14:36:41 - core.database - INFO - connect:40 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-01 14:36:41 - core.database - INFO - connect:43 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-01 14:36:42 - core.database - INFO - connect:57 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-01 14:36:42 - core.database - INFO - connect:60 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-01 14:36:42 - core.database - INFO - connect:74 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-01 14:36:42 - core.database - INFO - connect:77 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-01 14:36:42 - core.database - INFO - connect:88 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-01 14:36:42 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:36:42 - aiomysql - INFO - execute:243 - None
2025-08-01 14:36:42 - core.database - INFO - _test_database_connections:105 - dwoutput数据库连接测试成功: (1, 607898885, 'dwoutput')
2025-08-01 14:36:42 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:36:42 - aiomysql - INFO - execute:243 - None
2025-08-01 14:36:42 - core.database - INFO - _test_database_connections:112 - wedatas数据库连接测试成功: (1, 607898886, 'wedatas')
2025-08-01 14:36:42 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-01 14:36:42 - aiomysql - INFO - execute:243 - None
2025-08-01 14:36:42 - core.database - INFO - _test_database_connections:121 - welife_hydb数据库连接测试成功: (1,)
2025-08-01 14:36:42 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-01 14:36:42 - aiomysql - INFO - execute:243 - None
2025-08-01 14:36:42 - core.database - INFO - _test_database_connections:127 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-01 14:36:42 - core.database - INFO - _test_database_connections:138 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-01 14:36:42 - main - INFO - lifespan:27 - 数据库连接已建立
2025-08-01 14:38:20 - core.database - INFO - disconnect:155 - dwoutput数据库连接池已关闭
2025-08-01 14:38:20 - core.database - INFO - disconnect:160 - wedatas数据库连接池已关闭
2025-08-01 14:38:20 - core.database - INFO - disconnect:165 - welife_hydb数据库连接池已关闭
2025-08-01 14:38:20 - core.database - INFO - disconnect:169 - 品质收银数据库连接池已关闭
2025-08-01 14:38:20 - main - INFO - lifespan:33 - 应用已关闭
2025-08-01 14:38:23 - main - INFO - lifespan:23 - 应用启动中...
2025-08-01 14:38:23 - core.database - INFO - connect:26 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-01 14:38:24 - core.database - INFO - connect:40 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-01 14:38:24 - core.database - INFO - connect:43 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-01 14:38:24 - core.database - INFO - connect:57 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-01 14:38:24 - core.database - INFO - connect:60 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-01 14:38:24 - core.database - INFO - connect:74 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-01 14:38:24 - core.database - INFO - connect:77 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-01 14:38:24 - core.database - INFO - connect:88 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-01 14:38:24 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:38:24 - aiomysql - INFO - execute:243 - None
2025-08-01 14:38:24 - core.database - INFO - _test_database_connections:105 - dwoutput数据库连接测试成功: (1, 339463885, 'dwoutput')
2025-08-01 14:38:24 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:38:24 - aiomysql - INFO - execute:243 - None
2025-08-01 14:38:24 - core.database - INFO - _test_database_connections:112 - wedatas数据库连接测试成功: (1, 339463886, 'wedatas')
2025-08-01 14:38:24 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-01 14:38:24 - aiomysql - INFO - execute:243 - None
2025-08-01 14:38:24 - core.database - INFO - _test_database_connections:121 - welife_hydb数据库连接测试成功: (1,)
2025-08-01 14:38:24 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-01 14:38:24 - aiomysql - INFO - execute:243 - None
2025-08-01 14:38:24 - core.database - INFO - _test_database_connections:127 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-01 14:38:24 - core.database - INFO - _test_database_connections:138 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-01 14:38:24 - main - INFO - lifespan:27 - 数据库连接已建立
2025-08-01 14:38:58 - core.database - INFO - disconnect:155 - dwoutput数据库连接池已关闭
2025-08-01 14:38:58 - core.database - INFO - disconnect:160 - wedatas数据库连接池已关闭
2025-08-01 14:38:58 - core.database - INFO - disconnect:165 - welife_hydb数据库连接池已关闭
2025-08-01 14:38:58 - core.database - INFO - disconnect:169 - 品质收银数据库连接池已关闭
2025-08-01 14:38:58 - main - INFO - lifespan:33 - 应用已关闭
2025-08-01 14:39:01 - main - INFO - lifespan:23 - 应用启动中...
2025-08-01 14:39:01 - core.database - INFO - connect:26 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-01 14:39:01 - core.database - INFO - connect:40 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-01 14:39:01 - core.database - INFO - connect:43 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-01 14:39:01 - core.database - INFO - connect:57 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-01 14:39:01 - core.database - INFO - connect:60 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-01 14:39:02 - core.database - INFO - connect:74 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-01 14:39:02 - core.database - INFO - connect:77 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-01 14:39:02 - core.database - INFO - connect:88 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-01 14:39:02 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:39:02 - aiomysql - INFO - execute:243 - None
2025-08-01 14:39:02 - core.database - INFO - _test_database_connections:105 - dwoutput数据库连接测试成功: (1, 339464842, 'dwoutput')
2025-08-01 14:39:02 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:39:02 - aiomysql - INFO - execute:243 - None
2025-08-01 14:39:02 - core.database - INFO - _test_database_connections:112 - wedatas数据库连接测试成功: (1, 339464843, 'wedatas')
2025-08-01 14:39:02 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-01 14:39:02 - aiomysql - INFO - execute:243 - None
2025-08-01 14:39:02 - core.database - INFO - _test_database_connections:121 - welife_hydb数据库连接测试成功: (1,)
2025-08-01 14:39:02 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-01 14:39:02 - aiomysql - INFO - execute:243 - None
2025-08-01 14:39:02 - core.database - INFO - _test_database_connections:127 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-01 14:39:02 - core.database - INFO - _test_database_connections:138 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-01 14:39:02 - main - INFO - lifespan:27 - 数据库连接已建立
2025-08-01 14:39:39 - core.database - INFO - disconnect:155 - dwoutput数据库连接池已关闭
2025-08-01 14:39:39 - core.database - INFO - disconnect:160 - wedatas数据库连接池已关闭
2025-08-01 14:39:39 - core.database - INFO - disconnect:165 - welife_hydb数据库连接池已关闭
2025-08-01 14:39:39 - core.database - INFO - disconnect:169 - 品质收银数据库连接池已关闭
2025-08-01 14:39:39 - main - INFO - lifespan:33 - 应用已关闭
2025-08-01 14:39:42 - main - INFO - lifespan:23 - 应用启动中...
2025-08-01 14:39:42 - core.database - INFO - connect:26 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-01 14:39:42 - core.database - INFO - connect:40 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-01 14:39:42 - core.database - INFO - connect:43 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-01 14:39:42 - core.database - INFO - connect:57 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-01 14:39:42 - core.database - INFO - connect:60 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-01 14:39:43 - core.database - INFO - connect:74 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-01 14:39:43 - core.database - INFO - connect:77 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-01 14:39:43 - core.database - INFO - connect:88 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-01 14:39:43 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:39:43 - aiomysql - INFO - execute:243 - None
2025-08-01 14:39:43 - core.database - INFO - _test_database_connections:105 - dwoutput数据库连接测试成功: (1, 339465911, 'dwoutput')
2025-08-01 14:39:43 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:39:43 - aiomysql - INFO - execute:243 - None
2025-08-01 14:39:43 - core.database - INFO - _test_database_connections:112 - wedatas数据库连接测试成功: (1, 339465913, 'wedatas')
2025-08-01 14:39:43 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-01 14:39:43 - aiomysql - INFO - execute:243 - None
2025-08-01 14:39:43 - core.database - INFO - _test_database_connections:121 - welife_hydb数据库连接测试成功: (1,)
2025-08-01 14:39:43 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-01 14:39:43 - aiomysql - INFO - execute:243 - None
2025-08-01 14:39:43 - core.database - INFO - _test_database_connections:127 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-01 14:39:43 - core.database - INFO - _test_database_connections:138 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-01 14:39:43 - main - INFO - lifespan:27 - 数据库连接已建立
2025-08-01 14:40:01 - core.database - INFO - disconnect:155 - dwoutput数据库连接池已关闭
2025-08-01 14:40:01 - core.database - INFO - disconnect:160 - wedatas数据库连接池已关闭
2025-08-01 14:40:01 - core.database - INFO - disconnect:165 - welife_hydb数据库连接池已关闭
2025-08-01 14:40:02 - core.database - INFO - disconnect:169 - 品质收银数据库连接池已关闭
2025-08-01 14:40:02 - main - INFO - lifespan:33 - 应用已关闭
2025-08-01 14:40:04 - main - INFO - lifespan:23 - 应用启动中...
2025-08-01 14:40:04 - core.database - INFO - connect:26 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-01 14:40:04 - core.database - INFO - connect:40 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-01 14:40:04 - core.database - INFO - connect:43 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-01 14:40:05 - core.database - INFO - connect:57 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-01 14:40:05 - core.database - INFO - connect:60 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-01 14:40:05 - core.database - INFO - connect:74 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-01 14:40:05 - core.database - INFO - connect:77 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-01 14:40:05 - core.database - INFO - connect:88 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-01 14:40:05 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:40:05 - aiomysql - INFO - execute:243 - None
2025-08-01 14:40:05 - core.database - INFO - _test_database_connections:105 - dwoutput数据库连接测试成功: (1, 607904165, 'dwoutput')
2025-08-01 14:40:05 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:40:05 - aiomysql - INFO - execute:243 - None
2025-08-01 14:40:05 - core.database - INFO - _test_database_connections:112 - wedatas数据库连接测试成功: (1, 339466479, 'wedatas')
2025-08-01 14:40:05 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-01 14:40:05 - aiomysql - INFO - execute:243 - None
2025-08-01 14:40:05 - core.database - INFO - _test_database_connections:121 - welife_hydb数据库连接测试成功: (1,)
2025-08-01 14:40:05 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-01 14:40:05 - aiomysql - INFO - execute:243 - None
2025-08-01 14:40:05 - core.database - INFO - _test_database_connections:127 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-01 14:40:05 - core.database - INFO - _test_database_connections:138 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-01 14:40:05 - main - INFO - lifespan:27 - 数据库连接已建立
2025-08-01 14:40:18 - core.database - INFO - disconnect:155 - dwoutput数据库连接池已关闭
2025-08-01 14:40:18 - core.database - INFO - disconnect:160 - wedatas数据库连接池已关闭
2025-08-01 14:40:18 - core.database - INFO - disconnect:165 - welife_hydb数据库连接池已关闭
2025-08-01 14:40:18 - core.database - INFO - disconnect:169 - 品质收银数据库连接池已关闭
2025-08-01 14:40:18 - main - INFO - lifespan:33 - 应用已关闭
2025-08-01 14:40:21 - main - INFO - lifespan:23 - 应用启动中...
2025-08-01 14:40:21 - core.database - INFO - connect:26 - 正在创建dwoutput数据库连接... [用于存储DWOutput相关数据的数据库]
2025-08-01 14:40:21 - core.database - INFO - connect:40 - dwoutput数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/dwoutput
2025-08-01 14:40:21 - core.database - INFO - connect:43 - 正在创建wedatas数据库连接... [用于存储WeData相关数据的数据库]
2025-08-01 14:40:21 - core.database - INFO - connect:57 - wedatas数据库连接池创建成功: welife-dwoutput.rwlb.rds.aliyuncs.com:3306/wedatas
2025-08-01 14:40:21 - core.database - INFO - connect:60 - 正在创建welife_hydb数据库连接... [用于存储Welife相关数据的数据库]
2025-08-01 14:40:21 - core.database - INFO - connect:74 - welife_hydb数据库连接池创建成功: amv-2ze2zg89iw0m7txw500001131.ads.aliyuncs.com:3306/welife_hydb
2025-08-01 14:40:21 - core.database - INFO - connect:77 - 正在创建品质收银数据库连接... [品质收银数据库，用于存储POS相关数据]
2025-08-01 14:40:22 - core.database - INFO - connect:88 - 品质收银数据库连接池创建成功: gp-2zeb28sy9u8c6i667o-master.gpdb.rds.aliyuncs.com:5432/pos_dw
2025-08-01 14:40:22 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:40:22 - aiomysql - INFO - execute:243 - None
2025-08-01 14:40:22 - core.database - INFO - _test_database_connections:105 - dwoutput数据库连接测试成功: (1, 607904526, 'dwoutput')
2025-08-01 14:40:22 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result, CONNECTION_ID() AS connection_id, DATABASE() AS current_db
2025-08-01 14:40:22 - aiomysql - INFO - execute:243 - None
2025-08-01 14:40:22 - core.database - INFO - _test_database_connections:112 - wedatas数据库连接测试成功: (1, 607904528, 'wedatas')
2025-08-01 14:40:22 - aiomysql - INFO - execute:242 - SELECT 1 AS test_result
2025-08-01 14:40:22 - aiomysql - INFO - execute:243 - None
2025-08-01 14:40:22 - core.database - INFO - _test_database_connections:121 - welife_hydb数据库连接测试成功: (1,)
2025-08-01 14:40:22 - aiomysql - INFO - execute:242 - SELECT DATABASE() AS current_db
2025-08-01 14:40:22 - aiomysql - INFO - execute:243 - None
2025-08-01 14:40:22 - core.database - INFO - _test_database_connections:127 - welife_hydb当前数据库: ('welife_hydb',)
2025-08-01 14:40:22 - core.database - INFO - _test_database_connections:138 - 品质收银数据库连接测试成功: {'test_result': 1, 'current_db': 'pos_dw', 'pg_version': 'PostgreSQL 9.4.24 (Greenplum Database 6.3.0 build dev) on x86_64-unknown-linux-gnu, compiled by gcc (GCC) 9.2.1 20200522 (Alibaba 9.2.1-3 2.17), 64-bit compiled on May 17 2023 19:21:42'}
2025-08-01 14:40:22 - main - INFO - lifespan:27 - 数据库连接已建立
2025-08-01 14:56:53 - request - INFO - log_requests:70 - 收到请求: GET http://*************:8000/
2025-08-01 14:56:53 - request - INFO - log_requests:71 - 请求头: {'host': '*************:8000', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.117 Safari/537.36 ', 'accept': '*/*', 'accept-encoding': 'gzip'}
2025-08-01 14:56:53 - request - INFO - log_requests:78 - 请求处理完成: GET http://*************:8000/ - 状态码: 200 - 耗时: 0.001秒
